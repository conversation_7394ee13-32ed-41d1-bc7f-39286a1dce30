package usecase

import (
	"context"
	"fmt"
)

type adminAnalyticsUsecaseStub struct{}

func NewAdminAnalyticsUsecaseStub() AdminAnalyticsUsecase {
	return &adminAnalyticsUsecaseStub{}
}

func (uc *adminAnalyticsUsecaseStub) GetDashboardAnalytics(ctx context.Context, period string) (map[string]interface{}, error) {
	return nil, fmt.<PERSON><PERSON><PERSON>("admin dashboard analytics not implemented yet")
}

func (uc *adminAnalyticsUsecaseStub) GetRevenueAnalytics(ctx context.Context, period string, dateFrom, dateTo *string) (map[string]interface{}, error) {
	return nil, fmt.Errorf("admin revenue analytics not implemented yet")
}

func (uc *adminAnalyticsUsecaseStub) GetUsageAnalytics(ctx context.Context, period string, dateFrom, dateTo *string) (map[string]interface{}, error) {
	return nil, fmt.<PERSON><PERSON><PERSON>("admin usage analytics not implemented yet")
}

func (uc *adminAnalyticsUsecaseStub) ExportReport(ctx context.Context, reportType, format string, filters map[string]interface{}) (map[string]interface{}, error) {
	return nil, fmt.<PERSON>rrorf("admin export report not implemented yet")
}
