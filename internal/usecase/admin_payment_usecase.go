package usecase

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/smooth-inc/backend/internal/domain"
	"github.com/smooth-inc/backend/internal/repository"
)

type adminPaymentUsecase struct {
	paymentRepo repository.PaymentRepository
	sessionRepo repository.SessionRepository
}

func NewAdminPaymentUsecase(
	paymentRepo repository.PaymentRepository,
	sessionRepo repository.SessionRepository,
) AdminPaymentUsecase {
	return &adminPaymentUsecase{
		paymentRepo: paymentRepo,
		sessionRepo: sessionRepo,
	}
}

func (uc *adminPaymentUsecase) List(ctx context.Context, filters map[string]interface{}, limit, offset int) ([]*domain.Payment, int, error) {
	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}
	if offset < 0 {
		offset = 0
	}

	// Get all payments using GetByUserID for all users (basic implementation)
	// Since there's no List method, we'll need to implement this differently
	// For now, return empty list as this needs proper implementation
	payments := []*domain.Payment{}
	// TODO: Implement proper payment listing in repository

	// Apply filters
	var filteredPayments []*domain.Payment
	for _, payment := range payments {
		// Filter by status
		if status, ok := filters["status"].(domain.PaymentStatus); ok && payment.Status != status {
			continue
		}

		// Filter by user ID
		if userID, ok := filters["user_id"].(uuid.UUID); ok && payment.UserID != userID {
			continue
		}

		// Filter by session ID
		if sessionID, ok := filters["session_id"].(uuid.UUID); ok && payment.SessionID != sessionID {
			continue
		}

		// Filter by amount range
		if minAmount, ok := filters["min_amount"].(int); ok && payment.Amount < minAmount {
			continue
		}
		if maxAmount, ok := filters["max_amount"].(int); ok && payment.Amount > maxAmount {
			continue
		}

		// Filter by date range
		if dateFrom, ok := filters["date_from"].(time.Time); ok && payment.CreatedAt.Before(dateFrom) {
			continue
		}
		if dateTo, ok := filters["date_to"].(time.Time); ok && payment.CreatedAt.After(dateTo) {
			continue
		}

		// Filter by payment method (skip for now as field doesn't exist)
		// if paymentMethod, ok := filters["payment_method"].(string); ok {
		//     // TODO: Add payment method filtering when field is available
		// }

		filteredPayments = append(filteredPayments, payment)
	}

	// Apply pagination to filtered results
	total := len(filteredPayments)
	start := offset
	end := offset + limit

	if start >= total {
		return []*domain.Payment{}, total, nil
	}

	if end > total {
		end = total
	}

	paginatedPayments := filteredPayments[start:end]
	return paginatedPayments, total, nil
}

func (uc *adminPaymentUsecase) GetByID(ctx context.Context, id uuid.UUID) (*domain.Payment, error) {
	payment, err := uc.paymentRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get payment: %w", err)
	}
	return payment, nil
}

func (uc *adminPaymentUsecase) ProcessRefund(ctx context.Context, id uuid.UUID, amount *int, reason string) (*domain.Payment, error) {
	payment, err := uc.paymentRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get payment: %w", err)
	}

	if payment.Status != domain.PaymentStatusCompleted {
		return nil, fmt.Errorf("can only refund completed payments")
	}

	// Determine refund amount
	refundAmount := payment.Amount
	if amount != nil {
		if *amount <= 0 || *amount > payment.Amount {
			return nil, fmt.Errorf("invalid refund amount: must be between 1 and %d", payment.Amount)
		}
		refundAmount = *amount
	}

	// Create refund payment record
	refundPayment, err := domain.NewPayment(payment.SessionID, payment.UserID, refundAmount)
	if err != nil {
		return nil, fmt.Errorf("failed to create refund payment: %w", err)
	}

	// Set as refund type
	refundPayment.Currency = payment.Currency

	// Set original payment reference (if field exists)
	// refundPayment.OriginalPaymentID = &payment.ID

	// TODO: Process refund with Stripe
	// For now, just mark as completed
	refundPayment.MarkAsCompleted()

	// Save refund payment
	if err := uc.paymentRepo.Create(ctx, refundPayment); err != nil {
		return nil, fmt.Errorf("failed to save refund payment: %w", err)
	}

	// Update original payment status if full refund
	if refundAmount == payment.Amount {
		payment.MarkAsRefunded()
		if err := uc.paymentRepo.Update(ctx, payment); err != nil {
			return nil, fmt.Errorf("failed to update original payment: %w", err)
		}
	}

	// TODO: Add audit log for refund with reason
	// TODO: Send notification to user about refund

	return refundPayment, nil
}
