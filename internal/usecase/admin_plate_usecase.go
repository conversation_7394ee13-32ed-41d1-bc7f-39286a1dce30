package usecase

import (
	"context"
	"fmt"
	"strings"

	"github.com/google/uuid"
	"github.com/smooth-inc/backend/internal/domain"
	"github.com/smooth-inc/backend/internal/repository"
)

type adminPlateUsecase struct {
	plateRepo repository.PlateRepository
	userRepo  repository.UserRepository
}

func NewAdminPlateUsecase(plateRepo repository.PlateRepository, userRepo repository.UserRepository) AdminPlateUsecase {
	return &adminPlateUsecase{
		plateRepo: plateRepo,
		userRepo:  userRepo,
	}
}

func (uc *adminPlateUsecase) List(ctx context.Context, userID *uuid.UUID, plateNumber *string, plateType *domain.PlateType, isActive *bool, limit, offset int) ([]*domain.Plate, int, error) {
	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}
	if offset < 0 {
		offset = 0
	}

	// Get all plates using GetByUserID for all users (basic implementation)
	// Since there's no List method, we'll need to implement this differently
	// For now, return empty list as this needs proper implementation
	plates := []*domain.Plate{}
	// TODO: Implement proper plate listing in repository

	// Apply filters
	var filteredPlates []*domain.Plate
	for _, plate := range plates {
		// Filter by user ID
		if userID != nil && plate.UserID != *userID {
			continue
		}

		// Filter by plate number
		if plateNumber != nil && *plateNumber != "" {
			if !strings.Contains(strings.ToLower(plate.PlateNumber), strings.ToLower(*plateNumber)) {
				continue
			}
		}

		// Filter by plate type
		if plateType != nil && plate.PlateType != *plateType {
			continue
		}

		// Filter by active status
		if isActive != nil && plate.IsActive != *isActive {
			continue
		}

		filteredPlates = append(filteredPlates, plate)
	}

	// Apply pagination to filtered results
	total := len(filteredPlates)
	start := offset
	end := offset + limit

	if start >= total {
		return []*domain.Plate{}, total, nil
	}

	if end > total {
		end = total
	}

	paginatedPlates := filteredPlates[start:end]
	return paginatedPlates, total, nil
}

func (uc *adminPlateUsecase) GetByID(ctx context.Context, id uuid.UUID) (*domain.Plate, error) {
	plate, err := uc.plateRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get plate: %w", err)
	}
	return plate, nil
}

func (uc *adminPlateUsecase) Delete(ctx context.Context, id uuid.UUID) error {
	// Check if plate exists
	_, err := uc.plateRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get plate: %w", err)
	}

	if err := uc.plateRepo.Delete(ctx, id); err != nil {
		return fmt.Errorf("failed to delete plate: %w", err)
	}

	return nil
}

func (uc *adminPlateUsecase) Activate(ctx context.Context, id uuid.UUID) error {
	plate, err := uc.plateRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get plate: %w", err)
	}

	if plate.IsActive {
		return fmt.Errorf("plate is already active")
	}

	plate.Activate()

	if err := uc.plateRepo.Update(ctx, plate); err != nil {
		return fmt.Errorf("failed to activate plate: %w", err)
	}

	return nil
}

func (uc *adminPlateUsecase) Deactivate(ctx context.Context, id uuid.UUID) error {
	plate, err := uc.plateRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get plate: %w", err)
	}

	if !plate.IsActive {
		return fmt.Errorf("plate is already inactive")
	}

	plate.Deactivate()

	if err := uc.plateRepo.Update(ctx, plate); err != nil {
		return fmt.Errorf("failed to deactivate plate: %w", err)
	}

	return nil
}
