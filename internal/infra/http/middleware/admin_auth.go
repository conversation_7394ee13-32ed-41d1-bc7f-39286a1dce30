package middleware

import (
	"errors"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/smooth-inc/backend/internal/domain"
	"github.com/smooth-inc/backend/internal/infra/http/response"
	"github.com/smooth-inc/backend/internal/infra/logger"
)

// Helper function to get user ID from context
func getUserIDFromContext(c *gin.Context) (uuid.UUID, error) {
	userIDValue, exists := c.Get("user_id")
	if !exists {
		return uuid.Nil, errors.New("user ID not found in context")
	}

	userID, ok := userIDValue.(uuid.UUID)
	if !ok {
		return uuid.Nil, errors.New("invalid user ID type in context")
	}

	return userID, nil
}

func AdminAuthMiddleware(logger *logger.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		logger.LogInfo(c.Request.Context(), "Admin authorization check", map[string]interface{}{
			"method": c.Request.Method,
			"path":   c.Request.URL.Path,
		})

		// Get user ID from context (set by auth middleware)
		userID, err := getUserIDFromContext(c)
		if err != nil {
			logger.LogWarn(c.Request.Context(), "Admin access denied: no user ID in context", map[string]interface{}{
				"error": err.Error(),
				"path":  c.Request.URL.Path,
			})
			response.Unauthorized(c, "UNAUTHORIZED", "Authentication required")
			c.Abort()
			return
		}

		// Get user role from context (should be set by auth middleware)
		roleValue, exists := c.Get("user_role")
		if !exists {
			logger.LogWarn(c.Request.Context(), "Admin access denied: no user role in context", map[string]interface{}{
				"user_id": userID,
				"path":    c.Request.URL.Path,
			})
			response.Forbidden(c, "INSUFFICIENT_PERMISSIONS", "Admin access required")
			c.Abort()
			return
		}

		userRole, ok := roleValue.(domain.UserRole)
		if !ok {
			logger.LogWarn(c.Request.Context(), "Admin access denied: invalid user role type", map[string]interface{}{
				"user_id":   userID,
				"role_type": roleValue,
				"path":      c.Request.URL.Path,
			})
			response.Forbidden(c, "INSUFFICIENT_PERMISSIONS", "Admin access required")
			c.Abort()
			return
		}

		// Check if user has admin role
		if userRole != domain.UserRoleAdmin {
			logger.LogWarn(c.Request.Context(), "Admin access denied: insufficient permissions", map[string]interface{}{
				"user_id":   userID,
				"user_role": userRole,
				"path":      c.Request.URL.Path,
			})
			response.Forbidden(c, "INSUFFICIENT_PERMISSIONS", "Admin access required")
			c.Abort()
			return
		}

		logger.LogInfo(c.Request.Context(), "Admin access granted", map[string]interface{}{
			"user_id": userID,
			"path":    c.Request.URL.Path,
		})

		c.Next()
	}
}
